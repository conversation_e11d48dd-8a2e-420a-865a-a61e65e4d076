!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("index",[],t):"object"==typeof exports?exports.index=t():e.index=t()}(this,(()=>{return e={0:(e,t,o)=>{const s=o(216),n=o(599),r=o(719);e.exports=class{constructor(){this.faceTecRepository=new s}async execute({idScanResult:e,deviceKey:t,additionalHeaders:o={},onProgress:s=null}){const n=performance.now();try{r.logMessage("Starting PostIDScanOnlyUseCase execution"),r.logMessage("Preparing scan data...");const c=this.prepareScanData(e);r.logData("Scan Data Keys",Object.keys(c)),r.logMessage("Preparing headers...");const a=this.prepareHeaders(e,t,o);r.logData("Request Headers",Object.keys(a)),r.logMessage("Validating scan data..."),this.faceTecRepository.validateScanData(c),r.logSuccess("Scan data validation passed"),r.logMessage("Submitting to repository...");const i=await this.faceTecRepository.submitIDScan(c,a,s);r.logMessage("Processing response...");const l=this.processResponse(i),d=performance.now();return r.logPerformance("PostIDScanOnlyUseCase.execute",n,d),r.logSuccess(`UseCase completed successfully: ${l.success}`),l}catch(e){const t=performance.now();throw r.logPerformance("PostIDScanOnlyUseCase.execute (failed)",n,t),r.logError("PostIDScanOnlyUseCase - execute error",e),e}}prepareScanData(e){const t={idScan:e.idScan,enableConfirmInfo:!0};return e.frontImages&&e.frontImages[0]&&(t.idScanFrontImage=e.frontImages[0]),e.backImages&&e.backImages[0]&&(t.idScanBackImage=e.backImages[0]),t}prepareHeaders(e,t,o){const s={};return t&&(s["X-Device-Key"]=t),e.sessionId&&(s["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),o.Authorization&&(s.Authorization=o.Authorization),o["X-Session-Id"]&&(s["X-Session-Id"]=o["X-Session-Id"]),o["X-Ekyc-Token"]&&(s["X-Ekyc-Token"]=o["X-Ekyc-Token"]),o.correlationid&&(s.correlationid=o.correlationid),s["X-Tid"]=n.getUniqueId(),s}processResponse(e){return{success:!0===e.wasProcessed&&!1===e.error,scanResultBlob:e.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage}}}},103:e=>{e.exports=class{constructor(e){this.data=e}getToken(){return this.data?.token||null}getEkycToken(){return this.data?.data?.ekycToken?this.data.data.ekycToken:this.data?.ekycToken||null}getExpiresAt(){return this.data?.expiresAt||null}getCode(){return this.data?.code||null}getDescription(){return this.data?.description||null}isValid(){return!!this.getEkycToken()}toJSON(){return this.data}}},161:(e,t,o)=>{const s=o(103);e.exports=class{constructor(e){this.authApiDataSource=e}async getSessionToken(e={}){const t=await this.authApiDataSource.getSessionToken(e);return new s(t)}async getFaceTecSessionTokenWithEkycToken(e={}){const t=await this.authApiDataSource.getFaceTecSessionTokenWithEkycToken(e);return new s(t)}}},189:e=>{e.exports=class{constructor(e,t="USD"){this.amount=e,this.currencyCode=t}format(){return new Intl.NumberFormat("en-US",{style:"currency",currency:this.currencyCode}).format(this.amount)}getAmount(){return this.amount}getCurrencyCode(){return this.currencyCode}}},216:(e,t,o)=>{const s=o(518);e.exports=class{constructor(){this.idScanDataSource=new s}async submitIDScan(e,t={},o=null){try{return await this.idScanDataSource.postIDScanOnly(e,t,o)}catch(e){throw console.error("FaceTecRepository - submitIDScan error:",e),e}}validateScanData(e){if(!e)throw new Error("Scan data is required");if(!e.idScan)throw new Error("ID scan data is required");return!0}}},347:(e,t,o)=>{const s=o(592),n=o(863),r=o(995),c=o(985),a=o(161),i=o(548),{TokenStorage:l}=o(411),d=o(382),u=new a(new i),g=new s,p=new n,S=new r(u),k=new c(u),f=o(955),w=o(453),y=async(e={},t=!0)=>{try{e["X-Session-Id"]&&l.storeSessionId(e["X-Session-Id"]);const o=await S.execute(e),s=o.toJSON();if(t){const e=o.getEkycToken();e&&l.storeEkycToken(e)}return s}catch(e){throw console.error("Error getting session token:",e),e}},I=async(e={},t=!0)=>{try{const o=l.getSessionId();o&&!e["X-Session-Id"]&&(e={...e,"X-Session-Id":o});const s=(await k.execute(e)).toJSON();if(s.faceTecInitialized=!1,t&&s&&"CUS-KYC-1000"===s.code&&s.data&&s.data.deviceKey&&s.data.encryptionKey)try{await d.initializeFaceTec(s.data.deviceKey,s.data.encryptionKey),console.log("FaceTec SDK initialized successfully"),s.faceTecInitialized=!0}catch(e){console.error("Error initializing FaceTec SDK:",e),s.faceTecError=e.message||"Failed to initialize FaceTec SDK"}return s}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}},h=async(e={},t=null,s=null)=>{try{if(!t)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!s.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const n=await d.loadFaceTecSDK();n.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),n.setImagesDirectory("/core-sdk/FaceTec_images");const r={onComplete:(e,t,o)=>({sessionResult:e,idScanResult:t,networkResponseStatus:o})},c={"X-Session-Id":e["X-Session-Id"]||s.data?.sessionId,"X-Ekyc-Token":s.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||o(411).UuidGenerator.getUniqueId()},a=new f(s.data.sessionFaceTec,r,t,c);return new Promise(((e,t)=>{r.onComplete=(o,s,n)=>{a.isSuccess()?e({sessionResult:o,idScanResult:s,networkResponseStatus:n}):t(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}};e.exports={formatCurrency:(e,t="USD")=>g.execute(e,t),greet:e=>p.execute(e),getSessionToken:y,getStoredEkycToken:()=>l.getEkycToken(),clearEkycToken:()=>l.removeEkycToken(),getFaceTecSessionTokenWithEkycToken:I,performPhotoIDScan:h,initEkyc:async(e={})=>{const{sessionId:t,token:s,environment:n="development",language:r="en",initCallback:c}=e;if(!t)throw new Error("sessionId is required for eKYC initialization");if(!s)throw new Error("token is required for eKYC initialization");try{console.log("🚀 Initializing eKYC SDK with sessionId:",t);const{UuidGenerator:e}=o(411);let a=l.getToken("ekyc_device_id");a||(a=e.getUniqueId(),l.storeToken("ekyc_device_id",a)),l.storeToken("ekyc_session_id",t),l.storeToken("ekyc_api_token",s),l.storeToken("ekyc_environment",n),l.storeToken("ekyc_language",r);const i={Authorization:`Bearer ${s}`,"X-Session-Id":t,"X-Ekyc-Device-Info":`browser|${a}|${"undefined"!=typeof window?window.location.origin:"unknown"}|${r}|${r.toUpperCase()}`};console.log("📡 Getting session token...");const d=await y(i,!0);console.log("🎭 Getting FaceTec session token and initializing...");const u=await I(i,!0),g={success:!0,sessionToken:d,faceTecToken:u,environment:n,language:r,sessionId:t,initialized:!0,faceTecInitialized:u.faceTecInitialized||!1};return console.log("✅ eKYC SDK initialized successfully"),c&&"function"==typeof c&&c(g),g}catch(e){console.error("❌ Error initializing eKYC SDK:",e);const o={success:!1,error:e.message||"Failed to initialize eKYC SDK",environment:n,language:r,sessionId:t,initialized:!1};throw c&&"function"==typeof c&&c(o),e}},ocrIdCard:async(e={})=>{const{checkExpiredIdCard:t=!0,checkDopa:o=!1,enableConfirmInfo:s=!0,callback:n}=e;try{console.log("📄 Starting OCR ID Card scan...");const e=l.getToken("ekyc_session_id"),r=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const c={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${r}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const a=await I(c,!0);if(!a.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan...");const i={success:!0,ocrData:await h(c,r,a),checkExpiredIdCard:t,checkDopa:o,enableConfirmInfo:s,sessionId:e,scanType:"id_card_ocr"};return console.log("✅ OCR ID Card scan completed successfully"),n&&"function"==typeof n&&n(i),i}catch(e){console.error("❌ Error performing OCR ID card scan:",e);const r={success:!1,error:e.message||"Failed to perform OCR ID card scan",checkExpiredIdCard:t,checkDopa:o,enableConfirmInfo:s};throw n&&"function"==typeof n&&n(r),e}},ocrIdCardVerifyByFace:async(e={})=>{const{checkExpiredIdCard:t=!0,checkDopa:s=!1,enableConfirmInfo:n=!0,callback:r}=e;try{console.log("📄 Starting OCR ID Card with facial verification scan...");const e=l.getToken("ekyc_session_id"),c=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const a={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${c}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const i=await I(a,!0);if(!i.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan...");const u=await(async(e={},t=null,s=null)=>{try{if(!t)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!s.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const n=await d.loadFaceTecSDK();n.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),n.setImagesDirectory("/core-sdk/FaceTec_images");const r={onComplete:(e,t,o)=>({sessionResult:e,idScanResult:t,networkResponseStatus:o})},c={"X-Session-Id":e["X-Session-Id"]||s.data?.sessionId,"X-Ekyc-Token":s.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||o(411).UuidGenerator.getUniqueId()},a=new w(s.data.sessionFaceTec,r,t,c);return new Promise(((e,t)=>{r.onComplete=(o,s,n)=>{a.isSuccess()?e({sessionResult:o,idScanResult:s,networkResponseStatus:n}):t(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}})(a,c,i),g={success:!0,ocrData:u,checkExpiredIdCard:t,checkDopa:s,enableConfirmInfo:n,sessionId:e,scanType:"id_card_ocr"};return console.log("✅ OCR ID Card scan completed successfully"),r&&"function"==typeof r&&r(g),g}catch(e){console.error("❌ Error performing OCR ID card scan:",e);const o={success:!1,error:e.message||"Failed to perform OCR ID card scan",checkExpiredIdCard:t,checkDopa:s,enableConfirmInfo:n};throw r&&"function"==typeof r&&r(o),e}},ndidVerification:async(e={})=>{const{identifierType:t,identifierValue:o,serviceId:s,ndidVerificationCallback:n}=e;if(!t)throw new Error("identifierType is required for NDID verification");if(!o)throw new Error("identifierValue is required for NDID verification");if(!s)throw new Error("serviceId is required for NDID verification");try{console.log("🆔 Starting NDID verification...");const e=l.getToken("ekyc_session_id");if(l.getToken("ekyc_device_id"),!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const r=`ndid_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await new Promise((e=>setTimeout(e,2e3)));const c={success:!0,ndidVerified:!0,identifierType:t,identifierValue:o,serviceId:s,sessionId:e,verificationId:r,timestamp:(new Date).toISOString(),ndidResponse:{status:"verified",confidence:.95,details:{identityConfirmed:!0,documentValid:!0,biometricMatch:!0}}};return console.log("✅ NDID verification completed successfully"),n&&"function"==typeof n&&n(c),c}catch(e){console.error("❌ Error performing NDID verification:",e);const r={success:!1,error:e.message||"Failed to perform NDID verification",identifierType:t,identifierValue:o,serviceId:s};throw n&&"function"==typeof n&&n(r),e}},livenessCheck:async(e={})=>{const{livenessCheckCallback:t}=e;try{console.log("👁️ Starting liveness check...");const e=l.getToken("ekyc_session_id"),o=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const s={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${o}`};if(console.log("🎭 Ensuring FaceTec SDK is initialized..."),!(await I(s,!0)).faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");await d.loadFaceTecSDK(),console.log("🔍 Performing liveness detection..."),await new Promise((e=>setTimeout(e,3e3)));const n={success:!0,liveness:{sessionId:`liveness_${Date.now()}`,livenessScore:.92,isLive:!0,confidence:"high",timestamp:(new Date).toISOString()},sessionId:e,deviceId:o,faceTecInitialized:!0};return console.log("✅ Liveness check completed successfully"),t&&"function"==typeof t&&t(n),n}catch(e){console.error("❌ Error performing liveness check:",e);const o={success:!1,error:e.message||"Failed to perform liveness check"};throw t&&"function"==typeof t&&t(o),e}}}},382:(e,t,o)=>{const s=new(o(706));e.exports={loadFaceTecSDK:()=>s.loadFaceTecSDK(),initializeFaceTec:(e,t)=>s.initializeFaceTec(e,t),getFaceTecVersion:()=>s.getFaceTecVersion()}},411:(e,t,o)=>{const s=o(599),n=o(641);e.exports={UuidGenerator:s,TokenStorage:n}},453:(e,t,o)=>{const s=o(0),n=o(719);e.exports=function(e,t,o,r){var c=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=o||null,this.additionalHeaders=r||{},this.postIDScanOnlyUseCase=new s,this.processSessionResultWhileFaceTecSDKWaits=function(e,t){if(c.latestSessionResult=e,e.status!==FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully)return c.latestNetworkRequest.abort(),c.latestNetworkRequest=new XMLHttpRequest,void t.cancel();var o={faceScan:e.faceScan,auditTrailImage:e.auditTrail[0],lowQualityAuditTrailImage:e.lowQualityAuditTrail[0],sessionId:e.sessionId,externalDatabaseRefID:c.sampleAppControllerReference.getLatestEnrollmentIdentifier()};c.latestNetworkRequest=new XMLHttpRequest,c.latestNetworkRequest.open("POST",Config.BaseURL+"/enrollment-3d"),c.latestNetworkRequest.setRequestHeader("Content-Type","application/json"),c.latestNetworkRequest.setRequestHeader("X-Device-Key",Config.DeviceKeyIdentifier),c.latestNetworkRequest.setRequestHeader("X-User-Agent",FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),c.latestNetworkRequest.onreadystatechange=function(){if(c.latestNetworkRequest.readyState===XMLHttpRequest.DONE)try{var e=JSON.parse(c.latestNetworkRequest.responseText),o=e.scanResultBlob;!0===e.wasProcessed&&!1===e.error?(FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven"),t.proceedToNextStep(o)):c.cancelDueToNetworkError("Unexpected API response, cancelling out.",t)}catch(e){c.cancelDueToNetworkError("Exception while handling API response, cancelling out.",t)}},c.latestNetworkRequest.onerror=function(){c.cancelDueToNetworkError("XHR error, cancelling.",t)},c.latestNetworkRequest.upload.onprogress=function(e){var o=e.loaded/e.total;t.uploadProgress(o)};var s=JSON.stringify(o);c.latestNetworkRequest.send(s),window.setTimeout((function(){c.latestNetworkRequest.readyState!==XMLHttpRequest.DONE&&t.uploadMessageOverride("Still Uploading...")}),6e3)},this.processIDScanResultWhileFaceTecSDKWaits=function(e,t){if(c.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return c.latestNetworkRequest.abort(),c.latestNetworkRequest=new XMLHttpRequest,void t.cancel();var o={idScan:e.idScan,sessionId:e.sessionId,externalDatabaseRefID:c.sampleAppControllerReference.getLatestEnrollmentIdentifier(),minMatchLevel:3};e.frontImages&&e.frontImages[0]&&(o.idScanFrontImage=e.frontImages[0]),e.backImages&&e.backImages[0]&&(o.idScanBackImage=e.backImages[0]),c.latestNetworkRequest=new XMLHttpRequest,c.latestNetworkRequest.open("POST",Config.BaseURL+"/match-3d-2d-idscan"),c.latestNetworkRequest.setRequestHeader("Content-Type","application/json"),c.latestNetworkRequest.setRequestHeader("X-Device-Key",Config.DeviceKeyIdentifier),c.latestNetworkRequest.setRequestHeader("X-User-Agent",FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),c.latestNetworkRequest.onreadystatechange=function(){if(c.latestNetworkRequest.readyState===XMLHttpRequest.DONE)try{var e=JSON.parse(c.latestNetworkRequest.responseText),o=e.scanResultBlob;!0===e.wasProcessed&&!1===e.error?(FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"),t.proceedToNextStep(o)):!0===e.error&&null!=e.errorMessage?c.cancelDueToNetworkError(e.errorMessage,t):c.cancelDueToNetworkError("Unexpected API response, cancelling out.",t)}catch(e){c.cancelDueToNetworkError("Exception while handling API response, cancelling out.",t)}},c.latestNetworkRequest.onerror=function(){c.cancelDueToNetworkError("XHR error, cancelling.",t)},c.latestNetworkRequest.upload.onprogress=function(e){var o=e.loaded/e.total;t.uploadProgress(o)};var s=JSON.stringify(o);c.latestNetworkRequest.send(s)},this.onFaceTecSDKCompletelyDone=function(){null!=c.latestIDScanResult&&(c.success=c.latestIDScanResult.isCompletelyDone),c.success?n.logMessage("Id Scan Complete"):c.sampleAppControllerReference.clearLatestEnrollmentIdentifier(),c.sampleAppControllerReference.onComplete(c.latestSessionResult,c.latestIDScanResult,c.latestNetworkRequest.status)},this.cancelDueToNetworkError=function(e,t){!1===c.cancelledDueToNetworkError&&(console.error(e),c.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return c.success},this.success=!1,this.sampleAppControllerReference=t,this.latestSessionResult=null,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)}},518:(e,t,o)=>{const s=o(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanOnly(e,t={},o=null){return new Promise(((n,r)=>{const c=performance.now();try{const a=`${this.baseUrl}/idscan-only`;s.logApiCall(a,"POST","Starting request");const i=new XMLHttpRequest;o&&"function"==typeof o&&(i.upload.onprogress=function(e){if(e.lengthComputable){const t=e.loaded/e.total;s.logIDScanProgress("Uploading",t),o(t)}}),i.onreadystatechange=function(){if(i.readyState===XMLHttpRequest.DONE){const e=performance.now();try{if(i.status>=200&&i.status<300){const t=JSON.parse(i.responseText);s.logPerformance("IDScanDataSource.postIDScanOnly",c,e),s.logApiCall(a,"POST",`Success (${i.status})`),s.logData("API Response",{status:i.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob}),n(t)}else s.logPerformance("IDScanDataSource.postIDScanOnly (failed)",c,e),s.logError(`API call failed with status ${i.status}`),r(new Error(`HTTP error! status: ${i.status}`))}catch(e){s.logError("Failed to parse API response",e),r(new Error("Failed to parse response JSON"))}}},i.onerror=function(){const e=performance.now();s.logPerformance("IDScanDataSource.postIDScanOnly (network error)",c,e),s.logError("Network request failed"),r(new Error("Network request failed"))},i.open("POST",a),i.setRequestHeader("Content-Type","application/json"),Object.keys(t).forEach((e=>{void 0!==t[e]&&i.setRequestHeader(e,t[e])}));const l=JSON.stringify(e);s.logMessage(`Sending request to ${a} with ${Object.keys(e).length} data fields`),i.send(l)}catch(e){s.logError("IDScanDataSource - postIDScanOnly error",e),r(e)}}))}}},548:(e,t,o)=>{const s=o(599),{TokenStorage:n}=o(411);e.exports=class{async getSessionToken(e={}){try{const t="/api/session-token",o=e["X-Ekyc-Device-Info"]?null:s.getDeviceId(),r=s.getUniqueId(),c=e["X-Session-Id"]||s.getUniqueId();e["X-Session-Id"]||n.storeSessionId(c);const a={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${o}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${c}`,"X-Tid":`${r}`,correlationid:`${s.getUniqueId()}`,...e};e.Authorization&&(a.Authorization=e.Authorization);const i=await fetch(t,{method:"GET",headers:a});if(!i.ok)throw new Error(`API request failed with status ${i.status}`);return await i.json()}catch(e){throw console.error("Error getting session token:",e),e}}async getFaceTecSessionToken(e={}){try{const t="/api/facetec-session-token",o=e["X-Ekyc-Device-Info"]?null:s.getDeviceId(),r=s.getUniqueId(),c=n.getSessionId(),a={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${o}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${e["X-Session-Id"]||c||s.getUniqueId()}`,"X-Tid":`${r}`,correlationid:`${s.getUniqueId()}`,...e};e.Authorization&&(a.Authorization=e.Authorization);const i=await fetch(t,{method:"GET",headers:a});if(!i.ok)throw new Error(`API request failed with status ${i.status}`);return await i.json()}catch(e){throw console.error("Error getting FaceTec session token:",e),e}}async getFaceTecSessionTokenWithEkycToken(e={}){try{const t="/api/facetec-session-token",o=n.getEkycToken();if(!o)throw new Error("No eKYC token found. Please get a session token first.");const r=e["X-Ekyc-Device-Info"]?null:s.getDeviceId(),c=e["X-Tid"]||s.getUniqueId(),a=n.getSessionId(),i=e["X-Session-Id"]||a||s.getUniqueId(),l=e.correlationid||s.getUniqueId(),d={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":e["X-Ekyc-Sdk-Version"]||"1.0.0","X-Ekyc-Device-Info":e["X-Ekyc-Device-Info"]||`browser|${r}`,"X-Session-Id":`${i}`,"X-Tid":`${c}`,correlationid:`${l}`,"X-Ekyc-Token":o,...e};e.Authorization&&(d.Authorization=e.Authorization);const u=await fetch(t,{method:"GET",headers:d});if(!u.ok)throw new Error(`API request failed with status ${u.status}`);return await u.json()}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}}}},592:(e,t,o)=>{const s=o(189);e.exports=class{execute(e,t="USD"){return new s(e,t).format()}}},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},641:e=>{e.exports=class{static storeToken(e,t){if("undefined"!=typeof window&&window.localStorage&&t)try{return localStorage.setItem(e,t),!0}catch(e){return console.error("Error storing token:",e),!1}return!1}static getToken(e){return"undefined"!=typeof window&&window.localStorage?localStorage.getItem(e):null}static removeToken(e){if("undefined"!=typeof window&&window.localStorage)try{return localStorage.removeItem(e),!0}catch(e){return console.error("Error removing token:",e),!1}return!1}static storeEkycToken(e){return this.storeToken("ekyc_token",e)}static getEkycToken(){return this.getToken("ekyc_token")}static removeEkycToken(){return this.removeToken("ekyc_token")}static storeSessionId(e){return this.storeToken("ekyc_session_id",e)}static getSessionId(){return this.getToken("ekyc_session_id")}static removeSessionId(){return this.removeToken("ekyc_session_id")}}},706:e=>{e.exports=class{loadFaceTecSDK(){return new Promise(((e,t)=>{if("undefined"!=typeof window&&window.FaceTecSDK)return void e(window.FaceTecSDK);const o=document.createElement("script");o.src="/core-sdk/FaceTecSDK.js/FaceTecSDK.js",o.async=!0,o.onload=()=>{window.FaceTecSDK?e(window.FaceTecSDK):t(new Error("FaceTecSDK not found after loading script"))},o.onerror=()=>{t(new Error("Failed to load FaceTecSDK script"))},document.head.appendChild(o)}))}async initializeFaceTec(e,t){try{const o=await this.loadFaceTecSDK();return o.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),o.setImagesDirectory("/core-sdk/FaceTec_images"),new Promise(((s,n)=>{o.initializeInDevelopmentMode(e,t,(e=>{e?(console.log("FaceTecSDK initialized successfully"),s(!0)):(console.error("FaceTecSDK failed to initialize"),n(new Error("FaceTecSDK failed to initialize")))}))}))}catch(e){throw console.error("Error loading FaceTecSDK:",e),e}}async getFaceTecVersion(){try{return(await this.loadFaceTecSDK()).version()}catch(e){throw console.error("Error getting FaceTecSDK version:",e),e}}}},719:e=>{e.exports=class{static logMessage(e,t="info"){const o=`[${(new Date).toISOString()}] [${t.toUpperCase()}]`;switch(t){case"error":console.error(`${o} ${e}`);break;case"warn":console.warn(`${o} ${e}`);break;case"success":console.log(`%c${o} ${e}`,"color: green; font-weight: bold;");break;default:console.log(`${o} ${e}`)}}static logFaceTecStatus(e){this.logMessage(`FaceTec SDK: ${e}`,"info")}static logApiCall(e,t,o){this.logMessage(`API ${t} ${e}: ${o}`,"info")}static logSuccess(e){this.logMessage(e,"success")}static logError(e,t=null){let o=e;t&&(o+=` - ${t.message}`),this.logMessage(o,"error"),t&&t.stack&&console.error("Stack trace:",t.stack)}static logWarning(e){this.logMessage(e,"warn")}static logIDScanProgress(e,t=null){let o=`ID Scan: ${e}`;null!==t&&(o+=` (${Math.round(100*t)}%)`),this.logMessage(o,"info")}static logSession(e,t){this.logMessage(`Session ${e}: ${t}`,"info")}static clearConsole(){"function"==typeof console.clear&&console.clear()}static logData(e,t){console.group(`📊 ${e}`),console.log(t),console.groupEnd()}static logPerformance(e,t,o){const s=o-t;this.logMessage(`Performance: ${e} took ${s.toFixed(2)}ms`,"info")}}},863:e=>{e.exports=class{execute(e){return`Hello 12, ${e}!`}}},955:(e,t,o)=>{const s=o(0),n=o(719);e.exports=function(e,t,o,r){var c=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=o||null,this.additionalHeaders=r||{},this.postIDScanOnlyUseCase=new s,this.processIDScanResultWhileFaceTecSDKWaits=function(e,t){if(c.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return c.latestNetworkRequest.abort(),c.latestNetworkRequest=new XMLHttpRequest,void t.cancel();c.executeIDScanUseCase(e,t)},this.executeIDScanUseCase=async function(e,t){try{const o=function(e){t.uploadProgress(e)},s=await c.postIDScanOnlyUseCase.execute({idScanResult:e,deviceKey:c.deviceKey,additionalHeaders:c.additionalHeaders,onProgress:o});s.success?(FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"),t.proceedToNextStep(s.scanResultBlob)):c.cancelDueToNetworkError(s.errorMessage||"Unexpected API response, cancelling out.",t)}catch(e){console.error("PhotoIDScanProcessor - executeIDScanUseCase error:",e),c.cancelDueToNetworkError(e.message||"Exception while handling API response, cancelling out.",t)}},this.onFaceTecSDKCompletelyDone=function(){null!==c.latestIDScanResult&&(c.success=c.latestIDScanResult.isCompletelyDone),c.success&&n.logMessage("Id Scan Complete"),c.sampleAppControllerReference.onComplete(null,c.latestIDScanResult,200)},this.cancelDueToNetworkError=function(e,t){!1===c.cancelledDueToNetworkError&&(console.error(e),c.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return c.success},this.success=!1,this.sampleAppControllerReference=t,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)}},985:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getFaceTecSessionTokenWithEkycToken(e)}}},995:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getSessionToken(e)}}}},t={},function o(s){var n=t[s];if(void 0!==n)return n.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,o),r.exports}(347);var e,t}));